'use client'

import React, { createContext, useCallback, useContext, useMemo, useRef, useState } from 'react'

import { TBaseComponentProps } from '../../typings'

import Toast, { TToastProps } from './Toast'

const LocalToastContext = createContext<{
  show: (params: TToastProps) => void
  hide: () => void
}>({
  show: () => {},
  hide: () => {},
})

type TLocalToastProvider = TBaseComponentProps &
  Partial<TToastProps> & {
    containerRef: React.RefObject<HTMLElement>
  }

/**
 * 局部 Toast provider，用于在特定容器内显示 Toast
 */
const LocalToastProvider = (props: TLocalToastProvider) => {
  const { children, containerRef, ...providerProps } = props

  const [visible, setVisible] = useState(false)
  const [toastProps, setToastProps] = useState<Omit<TToastProps, 'visible'>>({ content: '' })
  const timeId = useRef<NodeJS.Timeout>()

  const hideToast = useCallback(() => {
    setVisible(false)
    setToastProps({ content: '' })
  }, [])

  const showToast = useCallback((params: Omit<TToastProps, 'visible'>) => {
    clearTimeout(timeId.current)

    timeId.current = setTimeout(() => {
      setVisible(true)
      setToastProps(params)
    }, 200)
  }, [])

  const contextValue = useMemo(() => {
    return {
      show: showToast,
      hide: hideToast,
    }
  }, [showToast, hideToast])

  return (
    <LocalToastContext.Provider value={contextValue}>
      {children}
      <Toast
        visible={visible}
        onClose={hideToast}
        containerRef={containerRef}
        {...providerProps}
        {...toastProps}
      />
    </LocalToastContext.Provider>
  )
}

export const useLocalToastContext = () => useContext(LocalToastContext)

LocalToastProvider.displayName = 'LocalToastProvider'

export default LocalToastProvider
