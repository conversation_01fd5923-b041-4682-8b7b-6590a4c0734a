import { gql } from '../../utils'

/**
 * 获取分类面包屑数据
 * @param {String} category_id - 分类ID
 */
export const GET_CATEGORY_BREADCRUMBS = gql`
  query getCategoryBreadcrumbs($category_id: String!) {
    categories(filters: { category_uid: { in: [$category_id] } }) {
      items {
        breadcrumbs {
          category_uid
          category_level
          category_name
          category_url_path
        }
        uid
        name
        url_path
        url_suffix
        level
      }
    }
  }
`
