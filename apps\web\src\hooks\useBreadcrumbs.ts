'use client'

import { useCallback } from 'react'
import { appLocalStorage } from '@ninebot/core'

export interface BreadcrumbItem {
  label: string
  href: string
}

const PRODUCT_CATEGORY_REFERRER_KEY = 'product_category_referrer_uid'

export const useBreadcrumbs = () => {
  // 保存分类referrer信息到appLocalStorage
  const saveCategoryReferrer = useCallback(async (categoryId: string) => {
    try {
      await appLocalStorage.setItem(PRODUCT_CATEGORY_REFERRER_KEY, categoryId)
    } catch (error) {
      console.warn('Failed to save category referrer:', error)
    }
  }, [])

  // 获取分类referrer信息
  const getCategoryReferrer = useCallback(async (): Promise<string | null> => {
    try {
      const value = await appLocalStorage.getItem<string>(PRODUCT_CATEGORY_REFERRER_KEY)
      // console.log('Breadcrumb Debug - Raw appLocalStorage value:', value)
      return value
    } catch (error) {
      console.warn('Failed to get category referrer:', error)
      return null
    }
  }, [])

  // 清理分类referrer信息
  const clearCategoryReferrer = useCallback(async () => {
    try {
      await appLocalStorage.removeItem(PRODUCT_CATEGORY_REFERRER_KEY)
    } catch (error) {
      console.warn('Failed to clear category referrer:', error)
    }
  }, [])

  // 不在组件卸载时自动清理referrer信息
  // 应该在产品页面处理完面包屑后再手动清理

  return {
    saveCategoryReferrer,
    getCategoryReferrer,
    clearCategoryReferrer,
  }
}
