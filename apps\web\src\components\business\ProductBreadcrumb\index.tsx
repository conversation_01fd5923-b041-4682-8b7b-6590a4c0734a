import { Breadcrumb as AntBreadcrumb } from 'antd'

import type { BreadcrumbItem } from '@/hooks/useBreadcrumbData'
import { Link } from '@/i18n/navigation'

interface ProductBreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
}

const ProductBreadcrumb = ({ items, className = '' }: ProductBreadcrumbProps) => {
  const breadcrumbElements = items.map((item, index) => {
    const isLast = index === items.length - 1

    return {
      title: isLast ? (
        <span className="text-pc-xs font-miSansMedium380 text-black">{item.label}</span>
      ) : (
        <Link
          href={item.href}
          className="text-gray-6 text-pc-xs hover:text-foreground font-miSansMedium380 transition-colors">
          {item.label}
        </Link>
      ),
      separator: index < items.length - 1 ? <span className="text-xs text-gray-4">/</span> : null,
    }
  })

  return (
    <div className={`py-[25px] pl-1 ${className}`}>
      <AntBreadcrumb items={breadcrumbElements} className="text-sm" />
    </div>
  )
}

export default ProductBreadcrumb
