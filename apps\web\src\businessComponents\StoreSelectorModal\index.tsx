'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import type { StoreList as StoreListType, StoreListItem } from '@ninebot/core'
import { formatDistance, Map, useToastContext } from '@ninebot/core'
import { useLocation } from '@ninebot/core/src/businessHooks'
import type { MassMarkerPoint } from '@ninebot/core/src/types/amap'

import { Modal, Skeleton } from '@/components'

import { Confirm } from '../Confirm'

import AddressSelector from './AddressSelector'
import StoreList from './StoreList'

// ==================== 类型定义 ====================

/**
 * 地址信息接口
 */
interface Address {
  region: string
  city: string
  district: string
}

/**
 * 组件属性接口
 */
interface StoreSelectorModalProps {
  doorVisible: boolean
  setDoorVisible: (visible: boolean) => void
  selectStore?: StoreListItem | null
  productId: string
  onConfirmCallback: (store: StoreListItem | null) => void
}

/**
 * 模态框状态枚举
 */
enum ModalState {
  LOADING = 'loading',
  LOCATION_PERMISSION = 'location_permission', // 定位权限
  ADDRESS_SELECTION = 'address_selection', // 地址选择
  STORE_SELECTION = 'store_selection', // 门店选择
}

// ==================== 常量定义 ====================

/**
 * 定位服务弹窗配置
 */
const LOCATION_MODAL_CONFIG = {
  title: '需要获取您的位置信息',
  content: '请授权定位信息以获取附近门店',
  cancelText: '取消',
  confirmText: '去设置',
} as const

// ==================== 图标组件 ====================

/**
 * 箭头图标组件
 */
const StoreSelectorArrow = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.56758 7.42851L3.74609 6.25L9.63865 12.1426L9.63865 13.3211L8.46014 13.3211L2.56758 7.42851Z"
      fill="#DA291C"
    />
    <path
      d="M17.4324 7.42851L16.2539 6.25L10.3614 12.1426L10.3614 13.3211L11.5399 13.3211L17.4324 7.42851Z"
      fill="#DA291C"
    />
  </svg>
)

// ==================== 主组件 ====================

/**
 * 门店选择器模态框组件
 *
 * 功能：
 * 1. 自动获取用户位置并显示附近门店
 * 2. 支持手动选择地址
 * 3. 支持门店列表展示和选择
 * 4. 支持定位权限引导
 * 5. 完善的错误处理和状态管理
 */
const StoreSelectorModal = ({
  doorVisible,
  setDoorVisible,
  productId,
  onConfirmCallback,
}: StoreSelectorModalProps) => {
  const getI18nString = useTranslations('Common')
  const { getLocation, reverseGeocode, openBrowserSettings } = useLocation()
  const toast = useToastContext()

  // ==================== 状态管理 ====================

  // 组件状态
  const [modalState, setModalState] = useState<ModalState>(ModalState.LOADING)

  // 数据状态
  const [store, setStore] = useState<StoreListType>([])
  const [curStore, setCurStore] = useState<StoreListItem | null>(null)
  const [address, setAddress] = useState<Address>({
    region: '',
    city: '',
    district: '',
  })
  const [userLocation, setUserLocation] = useState<{ longitude: number; latitude: number } | null>(
    null,
  )
  const [mapCenter, setMapCenter] = useState<{ longitude: number; latitude: number } | undefined>(
    undefined,
  )

  // UI状态
  const [btnLoading, setBtnLoading] = useState(false)
  const [initLoading, setInitLoading] = useState(true)

  // ==================== 工具函数 ====================

  /**
   * 显示定位服务弹窗
   */
  const showLocationServiceModal = useCallback(async () => {
    const shouldOpenSettings = await Confirm.confirm({
      title: LOCATION_MODAL_CONFIG.title,
      content: LOCATION_MODAL_CONFIG.content,
      okText: LOCATION_MODAL_CONFIG.confirmText,
      cancelText: LOCATION_MODAL_CONFIG.cancelText,
    })

    if (shouldOpenSettings) {
      setDoorVisible(false)
      openBrowserSettings()
    } else {
      // 用户拒绝定位，进入手动选择地址模式
      setModalState(ModalState.ADDRESS_SELECTION)
      setBtnLoading(true)
      setDoorVisible(true)
    }
  }, [setDoorVisible, openBrowserSettings])

  // ==================== 事件处理函数 ====================

  /**
   * 获取用户位置
   */
  const handleGetLocation = useCallback(async () => {
    try {
      const result = await getLocation()

      if (result && result.latitude) {
        // 获取到定位信息，进入门店选择模式
        setModalState(ModalState.STORE_SELECTION)
        setBtnLoading(true)
      } else {
        // 未授权定位信息，显示引导弹窗
        showLocationServiceModal()
      }
    } catch (error) {
      console.warn('获取定位信息失败，将使用手动选择地址:', error)

      // 定位失败时，进入手动选择地址模式
      setModalState(ModalState.ADDRESS_SELECTION)
      setBtnLoading(true)
      setDoorVisible(true)

      toast.show({
        icon: 'info',
        content: '定位失败，请手动选择门店地址',
      })
    }
  }, [getLocation, showLocationServiceModal, setDoorVisible, toast])

  /**
   * 关闭模态框
   */
  const handleCloseModal = useCallback(async () => {
    setDoorVisible(false)

    // 重置所有状态
    setModalState(ModalState.LOADING)
    setStore([])
    setCurStore(null)
    setBtnLoading(false)
    setInitLoading(true)
    setUserLocation(null)
    setAddress({
      region: '',
      city: '',
      district: '',
    })
    setMapCenter(undefined)
  }, [setDoorVisible])

  /**
   * 确认门店选择
   */
  const handleConfirmStore = useCallback(() => {
    if (curStore) {
      // 更新选中的门店
      setDoorVisible(false)
      onConfirmCallback(curStore)
    } else if (address.district && store.length === 0) {
      // 选择了地址但没有门店，直接关闭
      setDoorVisible(false)
      return
    } else {
      // 提示用户选择门店
      toast.show({
        icon: 'info',
        content: getI18nString('product_select_store_tip'),
      })
      return
    }
  }, [
    curStore,
    address.district,
    store.length,
    toast,
    getI18nString,
    onConfirmCallback,
    setDoorVisible,
  ])

  /**
   * 确认地址选择
   */
  const handleAddressConfirm = useCallback(async () => {
    setStore([])
    setCurStore(null)
    setBtnLoading(true)
    setModalState(ModalState.STORE_SELECTION)
  }, [])

  /**
   * 重新选择地址
   */
  const handleShowAddressSelection = useCallback(() => {
    setModalState(ModalState.ADDRESS_SELECTION)
    setStore([])
    setCurStore(null)
    setMapCenter(undefined)
  }, [])

  /**
   * 地址级联选择回调
   */
  const handleAddressChange = useCallback(
    (province: { label: string }, city: { label: string }, district: { label: string }) => {
      const hasProvince = !!province?.label
      setBtnLoading(!hasProvince)

      setAddress({
        region: province?.label || '',
        city: city?.label || '',
        district: district?.label || '',
      })
    },
    [],
  )

  /**
   * 处理地图标记点击
   */
  const handleMarkerClick = useCallback(
    (point: MassMarkerPoint) => {
      console.log('地图标记被点击:', point)

      // 根据点击的标记点找到对应的门店
      const clickedStore = store.find((storeItem) => {
        if (!storeItem) return false
        const storeLng = Number(storeItem.store_longitude)
        const storeLat = Number(storeItem.store_latitude)
        return storeLng === point.lnglat[0] && storeLat === point.lnglat[1]
      })

      if (clickedStore) {
        // 选中对应的门店
        setCurStore(clickedStore)

        // 滚动到对应的门店项
        const element = document.getElementById(`store-item-${clickedStore.store_id}`)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
        }

        console.log('已选中门店:', clickedStore.store_name)
      }
    },
    [store, setCurStore],
  )

  // ==================== 计算属性 ====================

  /**
   * 当前是否显示地址选择器
   */
  const showAddressSelector = useMemo(() => {
    return modalState === ModalState.ADDRESS_SELECTION
  }, [modalState])

  /**
   * 当前是否显示门店列表
   */
  const showStoreList = useMemo(() => {
    return modalState === ModalState.STORE_SELECTION
  }, [modalState])

  /**
   * 当前是否显示定位引导
   */
  const showLocationGuide = useMemo(() => {
    return modalState === ModalState.LOCATION_PERMISSION
  }, [modalState])

  /**
   * 按钮文本
   */
  const buttonText = useMemo(() => {
    if (showLocationGuide) return getI18nString('product_get_location')
    return getI18nString('confirm')
  }, [showLocationGuide, getI18nString])

  /**
   * 按钮点击处理函数
   */
  const buttonClickHandler = useMemo(() => {
    if (showLocationGuide) return handleGetLocation
    if (showStoreList) return handleConfirmStore
    return handleAddressConfirm
  }, [
    showLocationGuide,
    showStoreList,
    handleGetLocation,
    handleConfirmStore,
    handleAddressConfirm,
  ])

  /**
   * 按钮是否禁用
   */
  const isButtonDisabled = useMemo(() => {
    if (showLocationGuide) return false
    if (showStoreList) return btnLoading || !curStore
    return btnLoading
  }, [showLocationGuide, showStoreList, btnLoading, curStore])

  // ==================== 初始化逻辑 ====================

  /**
   * 初始化组件
   */
  const initializeComponent = useCallback(async () => {
    try {
      setStore([])
      setInitLoading(true)

      // 重置当前选中的门店，让用户重新选择
      setCurStore(null)

      // 每次都尝试重新获取当前位置
      try {
        const currentLocation = await getLocation()

        if (currentLocation && currentLocation.latitude && currentLocation.longitude) {
          // 保存位置信息
          setUserLocation({
            longitude: currentLocation.longitude,
            latitude: currentLocation.latitude,
          })

          // 同时设置地图中心点
          setMapCenter({
            longitude: currentLocation.longitude,
            latitude: currentLocation.latitude,
          })

          // 通过逆地理编码获取地址
          const res = await reverseGeocode(currentLocation.latitude, currentLocation.longitude)
          const addr = {
            region: res.regeocode.addressComponent.province,
            city: res.regeocode.addressComponent.city,
            district: res.regeocode.addressComponent.district,
          }

          setAddress(addr)
          setModalState(ModalState.STORE_SELECTION)
        } else {
          // 未获取到位置信息，进入手动选择地址模式
          setUserLocation(null)
          setMapCenter(undefined)
          setModalState(ModalState.ADDRESS_SELECTION)
          setBtnLoading(true)
          setAddress({
            region: '',
            city: '',
            district: '',
          })
        }
      } catch (error) {
        console.warn('获取位置信息失败，将使用手动选择地址:', error)
        // 定位失败，进入手动选择地址模式
        setUserLocation(null)
        setMapCenter(undefined)
        setModalState(ModalState.ADDRESS_SELECTION)
        setBtnLoading(true)
        setAddress({
          region: '',
          city: '',
          district: '',
        })
      }
    } catch (error) {
      console.error('初始化门店选择器失败:', error)
      toast.show({
        icon: 'fail',
        content: '定位失败，请手动选择门店',
      })

      // 初始化失败，进入地址选择模式
      setUserLocation(null)
      setMapCenter(undefined)
      setModalState(ModalState.ADDRESS_SELECTION)
      setBtnLoading(true)
    } finally {
      setInitLoading(false)
    }
  }, [getLocation, reverseGeocode, toast])

  // ==================== 副作用 ====================

  /**
   * 监听弹窗显示状态，初始化组件
   */
  useEffect(() => {
    if (doorVisible) {
      initializeComponent()
    }
  }, [doorVisible, initializeComponent])

  // 更新地图中心点为当前选中的门店位置
  useEffect(() => {
    if (curStore && curStore.store_latitude && curStore.store_longitude) {
      setMapCenter({
        longitude: Number(curStore.store_longitude),
        latitude: Number(curStore.store_latitude),
      })
    }
  }, [curStore])

  // 当门店数据更新时，如果没有用户位置和地图中心，使用第一个门店的位置作为地图中心
  useEffect(() => {
    if (!userLocation && !mapCenter && store && store.length > 0) {
      const firstStore = store[0]
      if (firstStore && firstStore.store_latitude && firstStore.store_longitude) {
        setMapCenter({
          longitude: Number(firstStore.store_longitude),
          latitude: Number(firstStore.store_latitude),
        })
      }
    }
  }, [store, userLocation, mapCenter])

  // 将所有可用门店和当前选中门店合并为海量点数据
  const storePoints = useMemo(() => {
    const points: MassMarkerPoint[] = []

    if (store && store.length > 0) {
      store.forEach((item) => {
        if (!item) return
        points.push({
          lnglat: [Number(item.store_longitude), Number(item.store_latitude)],
          name: item.store_name || '未知门店',
          // 如果是当前选中的门店，使用样式索引1（选中样式），否则使用默认样式0
          style: curStore && item.store_id === curStore.store_id ? 1 : 0,
        })
      })
    }

    return points
  }, [store, curStore])

  // ==================== 渲染函数 ====================

  /**
   * 渲染加载状态
   */
  const renderLoadingContent = () => (
    <div className="flex h-[448px] gap-base-16">
      {/* 左侧地图区域骨架屏 */}
      <div className="flex w-[600px] flex-col rounded-[4px]">
        {/* 地图骨架屏 */}
        <div className="flex-1">
          <Skeleton
            shape="square"
            style={{
              width: '100%',
              height: '100%',
              borderRadius: 12,
              backgroundColor: '#F3F3F4',
            }}
          />
        </div>
      </div>

      {/* 右侧内容区域骨架屏 */}
      <div className="flex flex-1 flex-col gap-base-16">
        {/* 地址显示骨架屏 */}
        <div className="flex flex-col gap-base">
          <Skeleton
            style={{
              width: 120,
              height: 20,
              borderRadius: 6,
              backgroundColor: '#F3F3F4',
            }}
          />
          <Skeleton
            style={{
              width: 200,
              height: 24,
              borderRadius: 8,
              backgroundColor: '#F3F3F4',
            }}
          />
        </div>

        {/* 内容区域骨架屏 */}
        <div className="flex-1 space-y-4">
          {Array.from({ length: 7 }, (_, index) => (
            <div key={index}>
              <Skeleton
                style={{
                  width: '100%',
                  height: 40,
                  borderRadius: 12,
                  backgroundColor: '#F3F3F4',
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  /**
   * 渲染定位引导状态
   */
  const renderLocationGuideContent = () => (
    <div className="flex h-[448px] items-center justify-center">
      <div className="text-center text-gray-500">
        <div className="mb-4 text-lg">目前还没有定位权限哦～</div>
        <div className="text-sm">请授权定位信息以获取附近门店</div>
      </div>
    </div>
  )

  /**
   * 渲染主要内容
   */
  const renderMainContent = () => (
    <div className="flex h-[448px] gap-base-16">
      {/* 左侧地图区域 */}
      <div className="flex w-[600px] flex-col rounded-lg bg-gray-50">
        {curStore && (
          <div className="space-y-4 bg-white pb-base-24">
            <div className="font-miSansMedium380 text-2xl">{curStore.store_name}</div>
            <div className="text-[#444446]">
              <div>
                {getI18nString('product_store_business')}{' '}
                {curStore.business_hours + '-' + curStore.closing_hours}
              </div>
              <div>
                {Number(curStore.store_distance) ? (
                  <span>
                    {getI18nString('product_store_distance')}
                    {formatDistance(Number(curStore.store_distance))}
                    {' | '}
                  </span>
                ) : null}
                {curStore.store_address}
              </div>
            </div>
          </div>
        )}
        <div className="flex-1">
          <Map
            mapCenter={mapCenter}
            points={storePoints}
            onMarkerClick={handleMarkerClick}
            zoom={curStore ? 17 : undefined}
          />
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex flex-1 flex-col gap-base-16">
        {/* 地址显示和选择 */}
        <div className="flex flex-col gap-base">
          <span className="text-[#444446]">{getI18nString('product_nearby_stores')}：</span>
          <div className="flex min-h-base-16 items-center gap-base">
            {address?.region ? (
              <>
                <button
                  className="flex items-center text-[#DA291C] hover:opacity-80"
                  onClick={handleShowAddressSelection}
                  aria-label="重新选择地区">
                  {address?.region} {address?.city} {address?.district}
                </button>
                {showStoreList && <StoreSelectorArrow />}
              </>
            ) : (
              <span className="text-[#444446]">请选择地址</span>
            )}
          </div>
        </div>

        {/* 门店列表 */}
        {showStoreList && (
          <StoreList
            productId={productId}
            store={store}
            setStore={setStore}
            setBtnLoading={setBtnLoading}
            curStore={curStore}
            setCurStore={setCurStore}
            address={address}
            userLocation={userLocation}
          />
        )}

        {/* 地址选择器 */}
        {showAddressSelector && (
          <AddressSelector
            onSelect={handleAddressChange}
            defaultValue={{
              province: address.region,
              city: address.city,
              district: address.district,
            }}
          />
        )}
      </div>
    </div>
  )

  /**
   * 渲染内容
   */
  const renderContent = () => {
    if (initLoading) {
      return renderLoadingContent()
    }

    if (showLocationGuide) {
      return renderLocationGuideContent()
    }

    return renderMainContent()
  }

  // ==================== 组件返回 ====================

  return (
    <Modal
      isOpen={doorVisible}
      onClose={handleCloseModal}
      onConfirm={buttonClickHandler}
      title={getI18nString('product_select_store')}
      width={944}
      okButtonProps={{
        disabled: isButtonDisabled,
      }}
      okText={buttonText}>
      {renderContent()}
    </Modal>
  )
}

export default StoreSelectorModal
