'use client'

import { useCallback, useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useLazyGetCategoryBreadcrumbsQuery } from '@ninebot/core'
import type { GetCategoryBreadcrumbsQuery } from '@ninebot/core/src/graphql/generated/graphql'

import { useBreadcrumbs } from './useBreadcrumbs'

export interface BreadcrumbItem {
  label: string
  href: string
}

export interface CategoryBreadcrumbData {
  category_level: number
  text: string
  path: string
  uid: string
}

export const useBreadcrumbData = (productData: { uid: string; name: string }) => {
  const { getCategoryReferrer, clearCategoryReferrer } = useBreadcrumbs()
  const getI18nString = useTranslations('Web')

  const [getCategoryBreadcrumbs, { data, isLoading, error }] = useLazyGetCategoryBreadcrumbsQuery()
  const [breadcrumbItems, setBreadcrumbItems] = useState<BreadcrumbItem[]>([])
  const [categoryId, setCategoryId] = useState<string | null>(null)

  // 构建基础面包屑（无分类路径）
  const buildBasicBreadcrumb = useCallback((): BreadcrumbItem[] => {
    return [
      { label: getI18nString('home'), href: '/' },
      { label: productData.name, href: '#' },
    ]
  }, [getI18nString, productData.name])

  // 构建完整的面包屑路径
  const buildFullBreadcrumb = useCallback(
    (categoryData: GetCategoryBreadcrumbsQuery): BreadcrumbItem[] => {
      const items: BreadcrumbItem[] = [{ label: getI18nString('home'), href: '/' }]

      const category = categoryData.categories?.items?.[0]
      if (!category?.breadcrumbs) {
        return buildBasicBreadcrumb()
      }

      // 按 category_level 排序
      const sortedBreadcrumbs = [...category.breadcrumbs]
        .filter((item): item is NonNullable<typeof item> => item !== null && item !== undefined)
        .sort((a, b) => (a.category_level ?? 0) - (b.category_level ?? 0))

      // 添加分类路径
      sortedBreadcrumbs.forEach((item) => {
        items.push({
          label: item.category_name ?? '',
          href: `/${item.category_url_path}${category.url_suffix || ''}`,
        })
      })

      // 添加当前产品
      items.push({
        label: productData.name,
        href: '#',
      })

      return items
    },
    [getI18nString, buildBasicBreadcrumb, productData.name],
  )

  // 获取referrer分类ID
  useEffect(() => {
    // 延迟一点时间确保appLocalStorage已经更新
    const timer = setTimeout(async () => {
      const referrerId = await getCategoryReferrer()
      // console.log('Breadcrumb Debug - Referrer ID from appLocalStorage (delayed):', referrerId)
      if (referrerId) {
        setCategoryId(referrerId)
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [getCategoryReferrer])

  // 获取分类面包屑数据
  useEffect(() => {
    if (categoryId) {
      // console.log('Breadcrumb Debug - Fetching category breadcrumbs for ID:', categoryId)
      getCategoryBreadcrumbs({ category_id: categoryId })
    }
  }, [categoryId, getCategoryBreadcrumbs])

  // 处理数据变化
  useEffect(() => {
    // console.log('Breadcrumb Debug - Data state:', { data, isLoading, error, categoryId })
    if (data && !isLoading && !error) {
      const category = data.categories?.items?.[0]
      // console.log('Breadcrumb Debug - Category data:', category)

      // 验证分类是否与产品匹配
      if (category) {
        const newItems = buildFullBreadcrumb(data)
        // console.log('Breadcrumb Debug - Built breadcrumb items:', newItems)
        setBreadcrumbItems(newItems)
        // 成功构建面包屑后清理referrer信息
        clearCategoryReferrer().catch((error) => {
          console.warn('Failed to clear category referrer:', error)
        })
      } else {
        // 分类数据不匹配，清理referrer并显示基础面包屑
        // console.log('Breadcrumb Debug - No category found, clearing referrer')
        clearCategoryReferrer().catch((error) => {
          console.warn('Failed to clear category referrer:', error)
        })
        setBreadcrumbItems(buildBasicBreadcrumb())
      }
    } else if (error) {
      // 查询失败，显示基础面包屑
      console.warn('Failed to fetch category breadcrumbs:', error)
      setBreadcrumbItems(buildBasicBreadcrumb())
    } else if (!categoryId) {
      // 没有referrer信息，显示基础面包屑
      // console.log('Breadcrumb Debug - No category ID, showing basic breadcrumb')
      setBreadcrumbItems(buildBasicBreadcrumb())
    }
  }, [
    data,
    isLoading,
    error,
    categoryId,
    clearCategoryReferrer,
    buildBasicBreadcrumb,
    buildFullBreadcrumb,
  ])

  return {
    breadcrumbItems,
    isLoading,
    error,
    categoryId,
  }
}
