'use client'
import React from 'react'

import { Skeleton } from '../skeleton'

interface BreadcrumbSkeletonProps {
  itemCount?: number
}

/**
 * 面包屑骨架屏组件
 */
const BreadcrumbSkeleton = ({ itemCount = 3 }: BreadcrumbSkeletonProps) => {
  return (
    <div className="py-base-24">
      <div className="flex items-center gap-2">
        {Array.from({ length: itemCount }, (_, index) => (
          <React.Fragment key={index}>
            <Skeleton
              style={{
                width: index === 0 ? 40 : index === itemCount - 1 ? 120 : 80,
                height: 16,
                borderRadius: 4,
              }}
            />
            {index < itemCount - 1 && <span className="text-[#D9D9D9]">/</span>}
          </React.Fragment>
        ))}
      </div>
    </div>
  )
}

export default BreadcrumbSkeleton
