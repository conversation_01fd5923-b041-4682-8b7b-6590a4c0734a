'use client'
import React from 'react'

import BreadcrumbSkeleton from '../breadcrumbSkeleton/BreadcrumbSkeleton'
import ProductCardSkeleton from '../productCardSkeleton/ProductCardSkeleton'
import { Skeleton } from '../skeleton'

interface ProductListSkeletonProps {
  hasCategories?: boolean
  showBanner?: boolean
  isSearchResult?: boolean
  total?: number
  isDigital?: boolean
  isLoggedIn?: boolean
  showBreadcrumb?: boolean
}

/**
 * 完整的产品列表骨架屏组件
 * 包含ProductFilter、Banner和ProductCard的骨架屏
 */
const ProductListSkeleton = ({
  hasCategories = false,
  showBanner = false,
  isSearchResult = false,
  total = 12,
  isDigital = false,
  isLoggedIn = false,
  showBreadcrumb = false,
}: ProductListSkeletonProps) => {
  return (
    <div className="w-full">
      {/* Breadcrumb 骨架屏 */}
      {showBreadcrumb && (
        <div className="border-b border-[#E1E1E4]">
          <BreadcrumbSkeleton itemCount={3} />
        </div>
      )}

      {/* ProductFilter 骨架屏 */}
      {!isSearchResult && (
        <div className={hasCategories ? 'mb-8' : 'mb-[32px]'}>
          {hasCategories ? (
            // 有分类时的筛选器骨架屏
            <div className="flex items-center justify-between">
              <Skeleton
                style={{
                  width: 120,
                  height: 20,
                  borderRadius: 4,
                }}
              />
              <div className="flex items-center gap-[12px]">
                <Skeleton
                  style={{
                    width: 80,
                    height: 20,
                    borderRadius: 4,
                  }}
                />
                <Skeleton
                  style={{
                    width: 16,
                    height: 16,
                    borderRadius: 2,
                  }}
                />
              </div>
            </div>
          ) : (
            // 无分类时的筛选器骨架屏
            <div className="flex items-center justify-between">
              <div className="flex gap-[16px]">
                <Skeleton
                  style={{
                    width: 60,
                    height: 30,
                    borderRadius: 8,
                  }}
                />
                <Skeleton
                  style={{
                    width: 60,
                    height: 30,
                    borderRadius: 8,
                  }}
                />
              </div>
              {isDigital && isLoggedIn && (
                <Skeleton
                  style={{
                    width: 100,
                    height: 20,
                    borderRadius: 4,
                  }}
                />
              )}
            </div>
          )}
        </div>
      )}

      {/* Banner 骨架屏 */}
      {!isSearchResult && showBanner && (
        <div className="relative my-base-32 w-full">
          <Skeleton
            style={{
              width: '100%',
              height: 576,
              borderRadius: 12,
            }}
          />
        </div>
      )}

      {/* 产品卡片骨架屏 */}
      <ProductCardSkeleton
        length={hasCategories ? 6 : total}
        perRow={hasCategories ? 3 : 4}
        listStyle="gap-x-[16px] gap-y-[32px]"
      />
    </div>
  )
}

export default ProductListSkeleton
